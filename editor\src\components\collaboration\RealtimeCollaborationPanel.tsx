/**
 * 实时协作面板组件
 * 提供实时协作功能的用户界面
 */
import React, { useState, useEffect } from 'react';
import {
  Card,
  Avatar,
  Badge,
  List,
  Button,
  Space,
  Typography,
  Row,
  Col,
  Tag,
  Modal,
  Form,
  Input,
  Select,
  Switch,
  Tooltip,
  Divider,
  Statistic,
  Timeline
} from 'antd';
import {
  UserOutlined,
  TeamOutlined,
  MessageOutlined,
  SettingOutlined,
  ShareAltOutlined,
  EyeOutlined,
  EditOutlined,
  SyncOutlined,
  WifiOutlined,
  DisconnectOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import RealtimeCollaborationService, {
  RealtimeUser,
  UserPresenceStatus,
  EditingZone
} from '../../services/RealtimeCollaborationService';
import './RealtimeCollaborationPanel.less';

const { Title, Text } = Typography;
const { Option } = Select;

interface RealtimeCollaborationPanelProps {
  visible: boolean;
  onClose: () => void;
}

const RealtimeCollaborationPanel: React.FC<RealtimeCollaborationPanelProps> = ({
  visible,
  onClose
}) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const [users, setUsers] = useState<RealtimeUser[]>([]);
  const [currentUser] = useState<RealtimeUser | null>(null);
  const [editingZones, setEditingZones] = useState<EditingZone[]>([]);
  const [isConnected, setIsConnected] = useState(false);
  const [collaborationStats, setCollaborationStats] = useState<any>({});
  const [activityHistory, setActivityHistory] = useState<any[]>([]);
  const [inviteModalVisible, setInviteModalVisible] = useState(false);
  const [settingsModalVisible, setSettingsModalVisible] = useState(false);

  const collaborationService = new RealtimeCollaborationService();

  useEffect(() => {
    if (visible) {
      setupEventListeners();
      loadCollaborationData();
    }

    return () => {
      cleanupEventListeners();
    };
  }, [visible]);

  const setupEventListeners = () => {
    collaborationService.on('userJoined', handleUserJoined);
    collaborationService.on('userLeft', handleUserLeft);
    collaborationService.on('userStatusUpdate', handleUserStatusUpdate);
    collaborationService.on('editingZoneEntered', handleEditingZoneEntered);
    collaborationService.on('editingZoneExited', handleEditingZoneExited);
    collaborationService.on('connected', handleConnected);
    collaborationService.on('disconnected', handleDisconnected);
    collaborationService.on('operationReceived', handleOperationReceived);
  };

  const cleanupEventListeners = () => {
    collaborationService.off('userJoined', handleUserJoined);
    collaborationService.off('userLeft', handleUserLeft);
    collaborationService.off('userStatusUpdate', handleUserStatusUpdate);
    collaborationService.off('editingZoneEntered', handleEditingZoneEntered);
    collaborationService.off('editingZoneExited', handleEditingZoneExited);
    collaborationService.off('connected', handleConnected);
    collaborationService.off('disconnected', handleDisconnected);
    collaborationService.off('operationReceived', handleOperationReceived);
  };

  const loadCollaborationData = () => {
    setUsers(Array.from(collaborationService['users'].values()));
    setCurrentUser(collaborationService['currentUser']);
    setEditingZones(Array.from(collaborationService['editingZones'].values()));
    setIsConnected(collaborationService['isConnected']);
    setCollaborationStats(collaborationService.getCollaborationStats());
  };

  const handleUserJoined = (user: RealtimeUser) => {
    setUsers(prev => [...prev, user]);
    setCollaborationStats(collaborationService.getCollaborationStats());
  };

  const handleUserLeft = (user: RealtimeUser) => {
    setUsers(prev => prev.filter(u => u.id !== user.id));
    setCollaborationStats(collaborationService.getCollaborationStats());
  };

  const handleUserStatusUpdate = (user: RealtimeUser) => {
    setUsers(prev => prev.map(u => u.id === user.id ? user : u));
  };

  const handleEditingZoneEntered = (zone: EditingZone) => {
    setEditingZones(prev => [...prev.filter(z => z.id !== zone.id), zone]);
  };

  const handleEditingZoneExited = (zone: EditingZone) => {
    setEditingZones(prev => prev.filter(z => z.id !== zone.id));
  };

  const handleConnected = () => {
    setIsConnected(true);
    setCollaborationStats(collaborationService.getCollaborationStats());
  };

  const handleDisconnected = () => {
    setIsConnected(false);
    setCollaborationStats(collaborationService.getCollaborationStats());
  };

  const handleOperationReceived = (operation: any) => {
    // 更新活动历史
    setActivityHistory(prev => [{
      id: operation.id,
      type: operation.type,
      user: users.find(u => u.id === operation.userId),
      timestamp: operation.timestamp,
      description: getOperationDescription(operation)
    }, ...prev.slice(0, 49)]); // 保留最近50条记录
  };

  const getOperationDescription = (operation: any): string => {
    switch (operation.type) {
      case 'object_create':
        return t('collaboration.objectCreated');
      case 'object_update':
        return t('collaboration.objectUpdated');
      case 'object_delete':
        return t('collaboration.objectDeleted');
      case 'property_update':
        return t('collaboration.propertyUpdated');
      default:
        return t('collaboration.unknownOperation');
    }
  };

  const handleInviteUser = async (values: any) => {
    try {
      // 这里应该调用邀请用户的API
      console.log('Inviting user:', values);
      setInviteModalVisible(false);
      form.resetFields();
    } catch (error) {
      console.error('Failed to invite user:', error);
    }
  };

  const handleForceSync = async () => {
    try {
      await collaborationService.forceSync();
    } catch (error) {
      console.error('Failed to force sync:', error);
    }
  };

  // 渲染用户列表
  const renderUsersList = () => (
    <div className="users-list">
      <div className="users-header">
        <Space>
          <Title level={5}>{t('collaboration.activeUsers')}</Title>
          <Badge count={users.length} showZero>
            <TeamOutlined />
          </Badge>
        </Space>
        <Button type="primary" size="small" onClick={() => setInviteModalVisible(true)}>
          <ShareAltOutlined />
          {t('collaboration.invite')}
        </Button>
      </div>

      <List
        dataSource={users}
        renderItem={(user) => (
          <List.Item className="user-item">
            <List.Item.Meta
              avatar={
                <Badge
                  status={user.status === UserPresenceStatus.ONLINE ? 'success' : 
                         user.status === UserPresenceStatus.AWAY ? 'warning' : 'default'}
                  dot
                >
                  <Avatar
                    src={user.avatar}
                    style={{ backgroundColor: user.color }}
                    icon={<UserOutlined />}
                  />
                </Badge>
              }
              title={
                <div className="user-info">
                  <Text strong>{user.name}</Text>
                  {user.isTyping && (
                    <Tag color="blue" style={{ marginLeft: 8 }}>
                      {t('collaboration.typing')}
                    </Tag>
                  )}
                </div>
              }
              description={
                <div>
                  <Text type="secondary" style={{ fontSize: '12px' }}>
                    {user.role} • {user.status}
                  </Text>
                  {user.editingZones.length > 0 && (
                    <div style={{ marginTop: 4 }}>
                      <Tag color="orange">
                        <EditOutlined />
                        {t('collaboration.editing')} {user.editingZones.length}
                      </Tag>
                    </div>
                  )}
                </div>
              }
            />
            <div className="user-actions">
              <Tooltip title={t('collaboration.viewUserActivity')}>
                <Button type="text" size="small" icon={<EyeOutlined />} />
              </Tooltip>
              <Tooltip title={t('collaboration.sendMessage')}>
                <Button type="text" size="small" icon={<MessageOutlined />} />
              </Tooltip>
            </div>
          </List.Item>
        )}
      />
    </div>
  );

  // 渲染编辑区域
  const renderEditingZones = () => (
    <div className="editing-zones">
      <Title level={5}>{t('collaboration.editingZones')}</Title>
      
      {editingZones.length === 0 ? (
        <Text type="secondary">{t('collaboration.noActiveEditing')}</Text>
      ) : (
        <List
          dataSource={editingZones}
          renderItem={(zone) => {
            const user = users.find(u => u.id === zone.userId);
            return (
              <List.Item className="editing-zone-item">
                <List.Item.Meta
                  avatar={
                    <Avatar
                      size="small"
                      src={user?.avatar}
                      style={{ backgroundColor: user?.color }}
                      icon={<UserOutlined />}
                    />
                  }
                  title={zone.entityId}
                  description={
                    <div>
                      <Text type="secondary" style={{ fontSize: '12px' }}>
                        {user?.name} • {zone.componentId || 'Entity'}
                      </Text>
                      <br />
                      <Text type="secondary" style={{ fontSize: '11px' }}>
                        {new Date(zone.startTime).toLocaleTimeString()}
                      </Text>
                    </div>
                  }
                />
                <Tag color={zone.isActive ? 'green' : 'default'}>
                  {zone.isActive ? t('collaboration.active') : t('collaboration.inactive')}
                </Tag>
              </List.Item>
            );
          }}
        />
      )}
    </div>
  );

  // 渲染活动历史
  const renderActivityHistory = () => (
    <div className="activity-history">
      <Title level={5}>{t('collaboration.recentActivity')}</Title>
      
      {activityHistory.length === 0 ? (
        <Text type="secondary">{t('collaboration.noRecentActivity')}</Text>
      ) : (
        <Timeline>
          {activityHistory.slice(0, 10).map((activity) => (
            <Timeline.Item key={activity.id}>
              <div className="activity-item">
                <Space>
                  <Avatar
                    size="small"
                    src={activity.user?.avatar}
                    style={{ backgroundColor: activity.user?.color }}
                    icon={<UserOutlined />}
                  />
                  <div>
                    <Text style={{ fontSize: '12px' }}>
                      <Text strong>{activity.user?.name}</Text> {activity.description}
                    </Text>
                    <br />
                    <Text type="secondary" style={{ fontSize: '11px' }}>
                      {new Date(activity.timestamp).toLocaleTimeString()}
                    </Text>
                  </div>
                </Space>
              </div>
            </Timeline.Item>
          ))}
        </Timeline>
      )}
    </div>
  );

  // 渲染连接状态
  const renderConnectionStatus = () => (
    <div className="connection-status">
      <Card size="small">
        <Row gutter={[16, 16]}>
          <Col span={12}>
            <Statistic
              title={t('collaboration.connectionStatus')}
              value={isConnected ? (t('collaboration.connected') || 'Connected') : (t('collaboration.disconnected') || 'Disconnected')}
              prefix={isConnected ? <WifiOutlined style={{ color: '#52c41a' }} /> : 
                                   <DisconnectOutlined style={{ color: '#ff4d4f' }} />}
              valueStyle={{ fontSize: '14px', color: isConnected ? '#52c41a' : '#ff4d4f' }}
            />
          </Col>
          <Col span={12}>
            <Statistic
              title={t('collaboration.activeUsers')}
              value={collaborationStats.connectedUsers || 0}
              prefix={<TeamOutlined />}
              valueStyle={{ fontSize: '14px' }}
            />
          </Col>
          <Col span={12}>
            <Statistic
              title={t('collaboration.editingZones')}
              value={collaborationStats.activeEditingZones || 0}
              prefix={<EditOutlined />}
              valueStyle={{ fontSize: '14px' }}
            />
          </Col>
          <Col span={12}>
            <Statistic
              title={t('collaboration.queueSize')}
              value={collaborationStats.messageQueueSize || 0}
              prefix={<SyncOutlined />}
              valueStyle={{ fontSize: '14px' }}
            />
          </Col>
        </Row>

        <Divider />

        <Space>
          <Button size="small" onClick={handleForceSync} disabled={!isConnected}>
            <SyncOutlined />
            {t('collaboration.forceSync')}
          </Button>
          <Button size="small" onClick={() => setSettingsModalVisible(true)}>
            <SettingOutlined />
            {t('collaboration.settings')}
          </Button>
        </Space>
      </Card>
    </div>
  );

  return (
    <Modal
      title={
        <Space>
          <TeamOutlined />
          {t('collaboration.realtimeCollaboration')}
          <Badge
            status={isConnected ? 'success' : 'error'}
            text={isConnected ? t('collaboration.connected') : t('collaboration.disconnected')}
          />
        </Space>
      }
      open={visible}
      onCancel={onClose}
      width={800}
      footer={[
        <Button key="close" onClick={onClose}>
          {t('common.close')}
        </Button>
      ]}
      className="realtime-collaboration-panel"
    >
      <Row gutter={[16, 16]}>
        <Col span={12}>
          {renderUsersList()}
        </Col>
        <Col span={12}>
          {renderEditingZones()}
          <Divider />
          {renderActivityHistory()}
        </Col>
        <Col span={24}>
          {renderConnectionStatus()}
        </Col>
      </Row>

      {/* 邀请用户对话框 */}
      <Modal
        title={t('collaboration.inviteUser')}
        open={inviteModalVisible}
        onCancel={() => setInviteModalVisible(false)}
        onOk={() => form.submit()}
      >
        <Form form={form} layout="vertical" onFinish={handleInviteUser}>
          <Form.Item
            name="email"
            label={t('collaboration.userEmail')}
            rules={[{ required: true, type: 'email' }]}
          >
            <Input />
          </Form.Item>
          <Form.Item name="role" label={t('collaboration.userRole')}>
            <Select defaultValue="editor">
              <Option value="viewer">{t('collaboration.viewer')}</Option>
              <Option value="editor">{t('collaboration.editor')}</Option>
              <Option value="admin">{t('collaboration.admin')}</Option>
            </Select>
          </Form.Item>
          <Form.Item name="message" label={t('collaboration.inviteMessage')}>
            <Input.TextArea rows={3} />
          </Form.Item>
        </Form>
      </Modal>

      {/* 设置对话框 */}
      <Modal
        title={t('collaboration.collaborationSettings')}
        open={settingsModalVisible}
        onCancel={() => setSettingsModalVisible(false)}
        footer={null}
      >
        <Form layout="vertical">
          <Form.Item label={t('collaboration.showCursors')}>
            <Switch defaultChecked />
          </Form.Item>
          <Form.Item label={t('collaboration.showSelections')}>
            <Switch defaultChecked />
          </Form.Item>
          <Form.Item label={t('collaboration.enableVoiceChat')}>
            <Switch />
          </Form.Item>
          <Form.Item label={t('collaboration.enableTextChat')}>
            <Switch defaultChecked />
          </Form.Item>
          <Form.Item label={t('collaboration.autoSave')}>
            <Switch defaultChecked />
          </Form.Item>
        </Form>
      </Modal>
    </Modal>
  );
};

export default RealtimeCollaborationPanel;
