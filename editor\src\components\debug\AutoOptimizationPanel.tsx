/**
 * 一键优化面板组件
 * 提供自动优化功能界面
 */
import React, { useState, useEffect } from 'react';
import {
  Card,
  Button,
  Progress,
  List,
  Space,
  Typography,
  Alert,
  Checkbox,
  Row,
  Col,
  Statistic,
  Tag,
  Modal,
  Switch
} from 'antd';
import {
  ThunderboltOutlined,
  PauseCircleOutlined,
  SettingOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  LoadingOutlined,
  WarningOutlined,
  InfoCircleOutlined,
  ClearOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import AutoOptimizationService, {
  OptimizationTask,
  OptimizationTaskStatus,
  OptimizationStats,
  BatchOptimizationConfig
} from '../../services/AutoOptimizationService';
import './AutoOptimizationPanel.less';

const { Title, Text, Paragraph } = Typography;

interface AutoOptimizationPanelProps {
  visible: boolean;
  onClose: () => void;
}

const AutoOptimizationPanel: React.FC<AutoOptimizationPanelProps> = ({
  visible,
  onClose
}) => {
  const { t } = useTranslation();
  const [isOptimizing, setIsOptimizing] = useState(false);
  const [tasks, setTasks] = useState<OptimizationTask[]>([]);
  const [stats, setStats] = useState<OptimizationStats | null>(null);
  const [config, setConfig] = useState<BatchOptimizationConfig | null>(null);
  const [configModalVisible, setConfigModalVisible] = useState(false);
  const [overallProgress, setOverallProgress] = useState(0);

  const autoOptimizationService = AutoOptimizationService.getInstance();

  useEffect(() => {
    if (visible) {
      loadData();
      setupEventListeners();
    }

    return () => {
      cleanupEventListeners();
    };
  }, [visible]);

  const setupEventListeners = () => {
    autoOptimizationService.on('optimizationStarted', handleOptimizationStarted);
    autoOptimizationService.on('optimizationCompleted', handleOptimizationCompleted);
    autoOptimizationService.on('optimizationFailed', handleOptimizationFailed);
    autoOptimizationService.on('optimizationCancelled', handleOptimizationCancelled);
    autoOptimizationService.on('taskStarted', handleTaskStarted);
    autoOptimizationService.on('taskProgress', handleTaskProgress);
    autoOptimizationService.on('taskCompleted', handleTaskCompleted);
  };

  const cleanupEventListeners = () => {
    autoOptimizationService.off('optimizationStarted', handleOptimizationStarted);
    autoOptimizationService.off('optimizationCompleted', handleOptimizationCompleted);
    autoOptimizationService.off('optimizationFailed', handleOptimizationFailed);
    autoOptimizationService.off('optimizationCancelled', handleOptimizationCancelled);
    autoOptimizationService.off('taskStarted', handleTaskStarted);
    autoOptimizationService.off('taskProgress', handleTaskProgress);
    autoOptimizationService.off('taskCompleted', handleTaskCompleted);
  };

  const loadData = () => {
    setTasks(autoOptimizationService.getOptimizationTasks());
    setConfig(autoOptimizationService.getConfig());
    setIsOptimizing(autoOptimizationService.isOptimizationInProgress());
  };

  const handleOptimizationStarted = () => {
    setIsOptimizing(true);
    setStats(null);
    setOverallProgress(0);
  };

  const handleOptimizationCompleted = (optimizationStats: OptimizationStats) => {
    setIsOptimizing(false);
    setStats(optimizationStats);
    setOverallProgress(100);
  };

  const handleOptimizationFailed = (error: any) => {
    setIsOptimizing(false);
    console.error('Optimization failed:', error);
  };

  const handleOptimizationCancelled = () => {
    setIsOptimizing(false);
    setOverallProgress(0);
  };

  const handleTaskStarted = (task: OptimizationTask) => {
    setTasks(prev => prev.map(t => t.id === task.id ? task : t));
  };

  const handleTaskProgress = (task: OptimizationTask) => {
    setTasks(prev => prev.map(t => t.id === task.id ? task : t));
    updateOverallProgress();
  };

  const handleTaskCompleted = (task: OptimizationTask) => {
    setTasks(prev => prev.map(t => t.id === task.id ? task : t));
    updateOverallProgress();
  };

  const updateOverallProgress = () => {
    const currentTasks = autoOptimizationService.getOptimizationTasks();
    if (currentTasks.length === 0) return;

    const totalProgress = currentTasks.reduce((sum, task) => sum + task.progress, 0);
    const avgProgress = totalProgress / currentTasks.length;
    setOverallProgress(avgProgress);
  };

  const handleStartOptimization = async () => {
    try {
      await autoOptimizationService.optimizeScene();
    } catch (error) {
      console.error('Failed to start optimization:', error);
    }
  };

  const handleCancelOptimization = () => {
    autoOptimizationService.cancelOptimization();
  };

  const handleConfigChange = (key: keyof BatchOptimizationConfig, value: any) => {
    if (!config) return;
    
    const newConfig = { ...config, [key]: value };
    setConfig(newConfig);
    autoOptimizationService.updateConfig(newConfig);
  };

  const handleClearHistory = () => {
    autoOptimizationService.clearTaskHistory();
    setTasks([]);
    setStats(null);
  };

  // 获取任务状态图标
  const getTaskStatusIcon = (status: OptimizationTaskStatus) => {
    switch (status) {
      case OptimizationTaskStatus.PENDING:
        return <InfoCircleOutlined style={{ color: '#1890ff' }} />;
      case OptimizationTaskStatus.RUNNING:
        return <LoadingOutlined style={{ color: '#faad14' }} />;
      case OptimizationTaskStatus.COMPLETED:
        return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
      case OptimizationTaskStatus.FAILED:
        return <CloseCircleOutlined style={{ color: '#ff4d4f' }} />;
      case OptimizationTaskStatus.CANCELLED:
        return <WarningOutlined style={{ color: '#d9d9d9' }} />;
      default:
        return <InfoCircleOutlined />;
    }
  };

  // 获取任务状态颜色
  const getTaskStatusColor = (status: OptimizationTaskStatus) => {
    switch (status) {
      case OptimizationTaskStatus.PENDING:
        return 'blue';
      case OptimizationTaskStatus.RUNNING:
        return 'orange';
      case OptimizationTaskStatus.COMPLETED:
        return 'green';
      case OptimizationTaskStatus.FAILED:
        return 'red';
      case OptimizationTaskStatus.CANCELLED:
        return 'default';
      default:
        return 'default';
    }
  };

  // 渲染配置对话框
  const renderConfigModal = () => (
    <Modal
      title={t('debug.autoOptimization.optimizationSettings')}
      open={configModalVisible}
      onCancel={() => setConfigModalVisible(false)}
      footer={[
        <Button key="close" onClick={() => setConfigModalVisible(false)}>
          {t('common.close')}
        </Button>
      ]}
    >
      {config && (
        <div className="optimization-config">
          <Title level={5}>{t('debug.autoOptimization.optimizationTypes')}</Title>
          <Space direction="vertical" style={{ width: '100%' }}>
            <Checkbox
              checked={config.enableGeometryOptimization}
              onChange={(e) => handleConfigChange('enableGeometryOptimization', e.target.checked)}
            >
              {t('debug.autoOptimization.geometryOptimization')}
            </Checkbox>
            <Checkbox
              checked={config.enableTextureOptimization}
              onChange={(e) => handleConfigChange('enableTextureOptimization', e.target.checked)}
            >
              {t('debug.autoOptimization.textureOptimization')}
            </Checkbox>
            <Checkbox
              checked={config.enableMaterialOptimization}
              onChange={(e) => handleConfigChange('enableMaterialOptimization', e.target.checked)}
            >
              {t('debug.autoOptimization.materialOptimization')}
            </Checkbox>
            <Checkbox
              checked={config.enableLightingOptimization}
              onChange={(e) => handleConfigChange('enableLightingOptimization', e.target.checked)}
            >
              {t('debug.autoOptimization.lightingOptimization')}
            </Checkbox>
            <Checkbox
              checked={config.enableBatchingOptimization}
              onChange={(e) => handleConfigChange('enableBatchingOptimization', e.target.checked)}
            >
              {t('debug.autoOptimization.batchingOptimization')}
            </Checkbox>
            <Checkbox
              checked={config.enableLODOptimization}
              onChange={(e) => handleConfigChange('enableLODOptimization', e.target.checked)}
            >
              {t('debug.autoOptimization.lodOptimization')}
            </Checkbox>
          </Space>

          <Title level={5} style={{ marginTop: 24 }}>
            {t('debug.autoOptimization.optimizationMode')}
          </Title>
          <Space direction="vertical" style={{ width: '100%' }}>
            <div>
              <Text>{t('debug.autoOptimization.aggressiveMode')}</Text>
              <Switch
                checked={config.aggressiveMode}
                onChange={(checked) => handleConfigChange('aggressiveMode', checked)}
                style={{ marginLeft: 8 }}
              />
            </div>
            <div>
              <Text>{t('debug.autoOptimization.preserveQuality')}</Text>
              <Switch
                checked={config.preserveQuality}
                onChange={(checked) => handleConfigChange('preserveQuality', checked)}
                style={{ marginLeft: 8 }}
              />
            </div>
          </Space>
        </div>
      )}
    </Modal>
  );

  return (
    <Modal
      title={
        <Space>
          <ThunderboltOutlined />
          {t('debug.autoOptimization.autoOptimization')}
        </Space>
      }
      open={visible}
      onCancel={onClose}
      width={800}
      footer={null}
      className="auto-optimization-panel"
    >
      <div className="optimization-controls">
        <Row gutter={[16, 16]}>
          <Col span={12}>
            <Button
              type="primary"
              size="large"
              icon={<ThunderboltOutlined />}
              onClick={handleStartOptimization}
              disabled={isOptimizing}
              block
            >
              {t('debug.autoOptimization.startOptimization')}
            </Button>
          </Col>
          <Col span={6}>
            <Button
              icon={<PauseCircleOutlined />}
              onClick={handleCancelOptimization}
              disabled={!isOptimizing}
              block
            >
              {t('debug.autoOptimization.cancel')}
            </Button>
          </Col>
          <Col span={6}>
            <Button
              icon={<SettingOutlined />}
              onClick={() => setConfigModalVisible(true)}
              block
            >
              {t('debug.autoOptimization.settings')}
            </Button>
          </Col>
        </Row>
      </div>

      {isOptimizing && (
        <Card title={t('debug.autoOptimization.optimizationProgress')} style={{ marginTop: 16 }}>
          <Progress
            percent={Math.round(overallProgress)}
            status={isOptimizing ? 'active' : 'normal'}
            strokeColor="#52c41a"
          />
          <Text type="secondary" style={{ marginTop: 8, display: 'block' }}>
            {t('debug.autoOptimization.processingTasks')}
          </Text>
        </Card>
      )}

      {stats && (
        <Card title={t('debug.autoOptimization.optimizationResults')} style={{ marginTop: 16 }}>
          <Row gutter={[16, 16]}>
            <Col span={6}>
              <Statistic
                title={t('debug.autoOptimization.fpsImprovement')}
                value={stats.totalImprovements.fps}
                suffix="fps"
                valueStyle={{ color: '#52c41a' }}
              />
            </Col>
            <Col span={6}>
              <Statistic
                title={t('debug.autoOptimization.memorySaved')}
                value={(stats.totalImprovements.memory / 1024 / 1024).toFixed(1)}
                suffix="MB"
                valueStyle={{ color: '#1890ff' }}
              />
            </Col>
            <Col span={6}>
              <Statistic
                title={t('debug.autoOptimization.drawCallsReduced')}
                value={stats.totalImprovements.drawCalls}
                valueStyle={{ color: '#722ed1' }}
              />
            </Col>
            <Col span={6}>
              <Statistic
                title={t('debug.autoOptimization.processingTime')}
                value={(stats.processingTime / 1000).toFixed(1)}
                suffix="s"
                valueStyle={{ color: '#faad14' }}
              />
            </Col>
          </Row>

          <div style={{ marginTop: 16 }}>
            <Text strong>
              {t('debug.autoOptimization.tasksSummary', {
                completed: stats.completedTasks,
                total: stats.totalTasks,
                failed: stats.failedTasks
              })}
            </Text>
          </div>
        </Card>
      )}

      {tasks.length > 0 && (
        <Card 
          title={t('debug.autoOptimization.taskList')}
          style={{ marginTop: 16 }}
          extra={
            <Button
              type="text"
              icon={<ClearOutlined />}
              onClick={handleClearHistory}
              disabled={isOptimizing}
            >
              {t('debug.autoOptimization.clearHistory')}
            </Button>
          }
        >
          <List
            dataSource={tasks}
            renderItem={(task) => (
              <List.Item>
                <List.Item.Meta
                  avatar={getTaskStatusIcon(task.status)}
                  title={
                    <Space>
                      <Text strong>{task.title}</Text>
                      <Tag color={getTaskStatusColor(task.status)}>
                        {task.status}
                      </Tag>
                    </Space>
                  }
                  description={
                    <div>
                      <Paragraph ellipsis={{ rows: 1 }}>{task.description}</Paragraph>
                      {task.status === OptimizationTaskStatus.RUNNING && (
                        <Progress
                          percent={Math.round(task.progress)}
                          size="small"
                          status="active"
                        />
                      )}
                      {task.error && (
                        <Alert
                          message={task.error}
                          type="error"
                          style={{ marginTop: 8 }}
                        />
                      )}
                      {task.result && task.result.warnings.length > 0 && (
                        <Alert
                          message={task.result.warnings.join(', ')}
                          type="warning"
                          style={{ marginTop: 8 }}
                        />
                      )}
                    </div>
                  }
                />
              </List.Item>
            )}
          />
        </Card>
      )}

      {tasks.length === 0 && !isOptimizing && (
        <Card style={{ marginTop: 16 }}>
          <div style={{ textAlign: 'center', padding: '40px 0' }}>
            <ThunderboltOutlined style={{ fontSize: '48px', color: '#d9d9d9' }} />
            <Title level={4} type="secondary">
              {t('debug.autoOptimization.noOptimizationHistory')}
            </Title>
            <Paragraph type="secondary">
              {t('debug.autoOptimization.clickStartToBegin')}
            </Paragraph>
          </div>
        </Card>
      )}

      {renderConfigModal()}
    </Modal>
  );
};

export default AutoOptimizationPanel;
