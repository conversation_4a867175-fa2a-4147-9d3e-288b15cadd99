/**
 * 语言切换器组件
 */

import React, { useState } from 'react';
import { Select, Button, Dropdown, Space, Tooltip, Spin } from 'antd';
import { GlobalOutlined, CheckOutlined, LoadingOutlined } from '@ant-design/icons';
import { useI18n, SupportedLocale } from '../../hooks/useI18n';
import './LanguageSwitcher.css';

export interface LanguageSwitcherProps {
  variant?: 'select' | 'dropdown' | 'button';
  size?: 'small' | 'middle' | 'large';
  showFlag?: boolean;
  showNativeName?: boolean;
  showEnglishName?: boolean;
  placement?: 'topLeft' | 'topCenter' | 'topRight' | 'bottomLeft' | 'bottomCenter' | 'bottomRight';
  className?: string;
  style?: React.CSSProperties;
}

export const LanguageSwitcher: React.FC<LanguageSwitcherProps> = ({
  variant = 'dropdown',
  size = 'middle',
  showFlag = true,
  showNativeName = true,
  showEnglishName = false,
  placement = 'bottomRight',
  className,
  style,
}) => {
  const { locale, localeConfig, isLoading, t, setLocale, getSupportedLocales } = useI18n();
  const [isChanging, setIsChanging] = useState(false);

  const supportedLocales = getSupportedLocales();

  const handleLocaleChange = async (newLocale: SupportedLocale) => {
    if (newLocale === locale || isChanging) {
      return;
    }

    setIsChanging(true);
    try {
      await setLocale(newLocale);
    } catch (error) {
      console.error('Failed to change locale:', error);
    } finally {
      setIsChanging(false);
    }
  };

  const renderLocaleLabel = (localeItem: typeof supportedLocales[0], compact = false) => {
    const parts = [];
    
    if (showFlag) {
      parts.push(
        <span key="flag" className="language-flag">
          {localeItem.flag}
        </span>
      );
    }
    
    if (showNativeName) {
      parts.push(
        <span key="native" className="language-native-name">
          {localeItem.nativeName}
        </span>
      );
    }
    
    if (showEnglishName && !compact) {
      parts.push(
        <span key="english" className="language-english-name">
          {localeItem.name}
        </span>
      );
    }

    return (
      <Space size={4} className="language-label">
        {parts}
      </Space>
    );
  };

  const renderCurrentLocale = () => {
    return renderLocaleLabel(localeConfig, true);
  };

  // Select变体
  if (variant === 'select') {
    return (
      <Select
        value={locale}
        onChange={handleLocaleChange}
        size={size}
        className={`language-switcher-select ${className || ''}`}
        style={style}
        loading={isLoading || isChanging}
        suffixIcon={isLoading || isChanging ? <LoadingOutlined /> : undefined}
        optionLabelProp="label"
      >
        {supportedLocales.map((localeItem) => (
          <Select.Option
            key={localeItem.code}
            value={localeItem.code}
            label={renderLocaleLabel(localeItem, true)}
          >
            <Space className="language-option">
              {renderLocaleLabel(localeItem)}
              {localeItem.code === locale && (
                <CheckOutlined className="language-check-icon" />
              )}
            </Space>
          </Select.Option>
        ))}
      </Select>
    );
  }

  // Button变体
  if (variant === 'button') {
    const menuItems = supportedLocales.map((localeItem) => ({
      key: localeItem.code,
      label: (
        <Space className="language-menu-item">
          {renderLocaleLabel(localeItem)}
          {localeItem.code === locale && (
            <CheckOutlined className="language-check-icon" />
          )}
        </Space>
      ),
      onClick: () => handleLocaleChange(localeItem.code),
      disabled: localeItem.code === locale || isChanging,
    }));

    return (
      <Dropdown
        menu={{ items: menuItems }}
        placement={placement}
        trigger={['click']}
        className={className}
      >
        <Button
          size={size}
          style={style}
          loading={isLoading || isChanging}
          icon={<GlobalOutlined />}
        >
          {renderCurrentLocale()}
        </Button>
      </Dropdown>
    );
  }

  // Dropdown变体（默认）
  const menuItems = supportedLocales.map((localeItem) => ({
    key: localeItem.code,
    label: (
      <Space className="language-menu-item">
        {renderLocaleLabel(localeItem)}
        {localeItem.code === locale && (
          <CheckOutlined className="language-check-icon" />
        )}
      </Space>
    ),
    onClick: () => handleLocaleChange(localeItem.code),
    disabled: localeItem.code === locale || isChanging,
  }));

  return (
    <Dropdown
      menu={{ items: menuItems }}
      placement={placement}
      trigger={['click']}
      className={className}
    >
      <Tooltip title={t('common.changeLanguage')}>
        <Button
          type="text"
          size={size}
          style={style}
          loading={isLoading || isChanging}
          icon={<GlobalOutlined />}
          className="language-switcher-button"
        >
          {renderCurrentLocale()}
        </Button>
      </Tooltip>
    </Dropdown>
  );
};

// 紧凑型语言切换器
export const CompactLanguageSwitcher: React.FC<{
  className?: string;
  style?: React.CSSProperties;
}> = ({ className, style }) => {
  return (
    <LanguageSwitcher
      variant="dropdown"
      size="small"
      showFlag={true}
      showNativeName={false}
      showEnglishName={false}
      className={className}
      style={style}
    />
  );
};

// 完整语言切换器
export const FullLanguageSwitcher: React.FC<{
  className?: string;
  style?: React.CSSProperties;
}> = ({ className, style }) => {
  return (
    <LanguageSwitcher
      variant="select"
      size="middle"
      showFlag={true}
      showNativeName={true}
      showEnglishName={true}
      className={className}
      style={style}
    />
  );
};

// 语言设置面板
export const LanguageSettingsPanel: React.FC = () => {
  const { locale, isLoading, t, getSupportedLocales } = useI18n();
  const supportedLocales = getSupportedLocales();

  if (isLoading) {
    return (
      <div className="language-settings-loading">
        <Spin size="large" />
        <p>{t('common.loading')}</p>
      </div>
    );
  }

  return (
    <div className="language-settings-panel">
      <h3>{t('settings.language.title')}</h3>
      <p className="language-settings-description">
        {t('settings.language.description')}
      </p>
      
      <div className="language-settings-current">
        <h4>{t('settings.language.current')}</h4>
        <div className="current-language-info">
          <Space size={12}>
            <span className="current-language-flag">
              {supportedLocales.find(l => l.code === locale)?.flag}
            </span>
            <div>
              <div className="current-language-native">
                {supportedLocales.find(l => l.code === locale)?.nativeName}
              </div>
              <div className="current-language-english">
                {supportedLocales.find(l => l.code === locale)?.name}
              </div>
            </div>
          </Space>
        </div>
      </div>

      <div className="language-settings-switcher">
        <h4>{t('settings.language.change')}</h4>
        <FullLanguageSwitcher />
      </div>

      <div className="language-settings-info">
        <h4>{t('settings.language.supported')}</h4>
        <div className="supported-languages-list">
          {supportedLocales.map((localeItem) => (
            <div key={localeItem.code} className="supported-language-item">
              <Space>
                <span>{localeItem.flag}</span>
                <span>{localeItem.nativeName}</span>
                <span className="supported-language-english">
                  ({localeItem.name})
                </span>
                {localeItem.code === locale && (
                  <CheckOutlined className="current-language-indicator" />
                )}
              </Space>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};
