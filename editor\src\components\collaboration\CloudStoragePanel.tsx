/**
 * 云端存储面板组件
 * 提供云端项目存储、文件管理、同步控制等功能
 */
import React, { useState, useEffect } from 'react';
import {
  Card,
  Tabs,
  Button,
  Space,
  Typography,
  Row,
  Col,
  List,
  Tag,
  Modal,
  Form,
  Input,
  Select,
  Progress,
  Upload,
  Tooltip,
  Badge,
  Statistic,
  Table,
  Alert,
  Switch,
  Slider,
  Divider
} from 'antd';
import {
  CloudOutlined,
  SyncOutlined,
  UploadOutlined,
  DownloadOutlined,
  FolderOutlined,
  FileOutlined,
  SettingOutlined,
  DeleteOutlined,
  ShareAltOutlined,
  HistoryOutlined,
  PlusOutlined,
  ExportOutlined,
  ImportOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import CloudStorageService, {
  CloudProject,
  CloudFile,
  FileStatus,
  SyncStrategy,
  StorageProvider,
  CompressionAlgorithm,
  UploadProgress,
  DownloadProgress,
  SyncProgress
} from '../../services/CloudStorageService';
import './CloudStoragePanel.less';

const { Text } = Typography;
const { TabPane } = Tabs;
const { Option } = Select;
const { Dragger } = Upload;

interface CloudStoragePanelProps {
  visible: boolean;
  onClose: () => void;
}

const CloudStoragePanel: React.FC<CloudStoragePanelProps> = ({
  visible,
  onClose
}) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const [activeTab] = useState('projects');
  const [projects, setProjects] = useState<CloudProject[]>([]);
  const [activeProject, setActiveProject] = useState<CloudProject | null>(null);
  const [files, setFiles] = useState<CloudFile[]>([]);
  const [isOnline, setIsOnline] = useState(true);
  const [syncStatus, setSyncStatus] = useState<any>({});
  const [storageUsage, setStorageUsage] = useState<any>({});
  const [uploadProgress, setUploadProgress] = useState<UploadProgress | null>(null);
  const [downloadProgress, setDownloadProgress] = useState<DownloadProgress | null>(null);
  const [syncProgress, setSyncProgress] = useState<SyncProgress | null>(null);
  const [newProjectModalVisible, setNewProjectModalVisible] = useState(false);
  const [settingsModalVisible, setSettingsModalVisible] = useState(false);

  const cloudStorageService = CloudStorageService.getInstance();

  useEffect(() => {
    if (visible) {
      loadData();
      setupEventListeners();
    }

    return () => {
      cleanupEventListeners();
    };
  }, [visible]);

  const setupEventListeners = () => {
    cloudStorageService.on('projectCreated', handleProjectCreated);
    cloudStorageService.on('projectOpened', handleProjectOpened);
    cloudStorageService.on('fileUploaded', handleFileUploaded);
    cloudStorageService.on('fileDownloaded', handleFileDownloaded);
    cloudStorageService.on('uploadProgress', setUploadProgress);
    cloudStorageService.on('downloadProgress', setDownloadProgress);
    cloudStorageService.on('syncProgress', setSyncProgress);
    cloudStorageService.on('syncCompleted', handleSyncCompleted);
    cloudStorageService.on('networkStatusChanged', handleNetworkStatusChanged);
  };

  const cleanupEventListeners = () => {
    cloudStorageService.off('projectCreated', handleProjectCreated);
    cloudStorageService.off('projectOpened', handleProjectOpened);
    cloudStorageService.off('fileUploaded', handleFileUploaded);
    cloudStorageService.off('fileDownloaded', handleFileDownloaded);
    cloudStorageService.off('uploadProgress', setUploadProgress);
    cloudStorageService.off('downloadProgress', setDownloadProgress);
    cloudStorageService.off('syncProgress', setSyncProgress);
    cloudStorageService.off('syncCompleted', handleSyncCompleted);
    cloudStorageService.off('networkStatusChanged', handleNetworkStatusChanged);
  };

  const loadData = () => {
    setProjects(cloudStorageService.getAllProjects());
    setActiveProject(cloudStorageService.getActiveProject());
    setIsOnline(cloudStorageService.isNetworkOnline());
    setSyncStatus(cloudStorageService.getSyncStatus());
    setStorageUsage(cloudStorageService.getStorageUsage());
    
    const activeProj = cloudStorageService.getActiveProject();
    if (activeProj) {
      setFiles(activeProj.files);
    }
  };

  const handleProjectCreated = (project: CloudProject) => {
    setProjects(prev => [...prev, project]);
    setStorageUsage(cloudStorageService.getStorageUsage());
  };

  const handleProjectOpened = (project: CloudProject) => {
    setActiveProject(project);
    setFiles(project.files);
  };

  const handleFileUploaded = (file: CloudFile) => {
    setFiles(prev => prev.map(f => f.id === file.id ? file : f));
    setStorageUsage(cloudStorageService.getStorageUsage());
  };

  const handleFileDownloaded = (file: CloudFile) => {
    setFiles(prev => prev.map(f => f.id === file.id ? file : f));
  };

  const handleSyncCompleted = () => {
    setSyncProgress(null);
    setSyncStatus(cloudStorageService.getSyncStatus());
    loadData();
  };

  const handleNetworkStatusChanged = ({ online }: { online: boolean }) => {
    setIsOnline(online);
  };

  const handleCreateProject = async (values: any) => {
    try {
      const project = await cloudStorageService.createProject(
        values.name,
        values.description,
        {
          provider: values.provider,
          compression: values.compression,
          encryption: values.encryption
        }
      );
      
      setNewProjectModalVisible(false);
      form.resetFields();
      
      // 自动打开新项目
      await cloudStorageService.openProject(project.id);
    } catch (error) {
      console.error('Failed to create project:', error);
    }
  };

  const handleOpenProject = async (projectId: string) => {
    try {
      await cloudStorageService.openProject(projectId);
    } catch (error) {
      console.error('Failed to open project:', error);
    }
  };

  const handleUploadFiles = async (fileList: any[]) => {
    if (!activeProject) return;

    for (const file of fileList) {
      try {
        await cloudStorageService.uploadFile(file.originFileObj, `/${file.name}`);
      } catch (error) {
        console.error('Failed to upload file:', error);
      }
    }
  };

  const handleDownloadFile = async (fileId: string) => {
    try {
      const blob = await cloudStorageService.downloadFile(fileId);
      const file = files.find(f => f.id === fileId);
      if (file) {
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = file.name;
        a.click();
        URL.revokeObjectURL(url);
      }
    } catch (error) {
      console.error('Failed to download file:', error);
    }
  };

  const handleDeleteFile = async (fileId: string) => {
    Modal.confirm({
      title: t('cloudStorage.confirmDeleteFile'),
      content: t('cloudStorage.confirmDeleteFileContent'),
      onOk: async () => {
        try {
          await cloudStorageService.deleteFile(fileId);
          setFiles(prev => prev.filter(f => f.id !== fileId));
        } catch (error) {
          console.error('Failed to delete file:', error);
        }
      }
    });
  };

  const handleForceSync = async () => {
    try {
      await cloudStorageService.forceSync();
    } catch (error) {
      console.error('Failed to sync:', error);
    }
  };

  // 渲染项目列表
  const renderProjectsList = () => (
    <div className="projects-list">
      <div className="projects-header">
        <Space>
          <Button type="primary" onClick={() => setNewProjectModalVisible(true)}>
            <PlusOutlined />
            {t('cloudStorage.newProject')}
          </Button>
          <Button onClick={() => setSettingsModalVisible(true)}>
            <SettingOutlined />
            {t('cloudStorage.settings')}
          </Button>
        </Space>
      </div>

      <List
        dataSource={projects}
        renderItem={(project) => (
          <List.Item
            className={`project-item ${activeProject?.id === project.id ? 'active' : ''}`}
            onClick={() => handleOpenProject(project.id)}
            actions={[
              <Tooltip title={t('cloudStorage.sync')}>
                <Button
                  type="text"
                  icon={<SyncOutlined />}
                  onClick={(e) => {
                    e.stopPropagation();
                    handleForceSync();
                  }}
                />
              </Tooltip>,
              <Tooltip title={t('cloudStorage.export')}>
                <Button
                  type="text"
                  icon={<ExportOutlined />}
                  onClick={(e) => {
                    e.stopPropagation();
                    cloudStorageService.exportProject(project.id);
                  }}
                />
              </Tooltip>
            ]}
          >
            <List.Item.Meta
              avatar={
                <div className="project-icon">
                  <CloudOutlined />
                </div>
              }
              title={
                <div>
                  <Text strong>{project.name}</Text>
                  {!isOnline && (
                    <Tag color="orange" style={{ marginLeft: 8 }}>
                      {t('cloudStorage.offline')}
                    </Tag>
                  )}
                </div>
              }
              description={
                <div>
                  <Text type="secondary" style={{ fontSize: '12px' }}>
                    {project.description}
                  </Text>
                  <br />
                  <Space style={{ marginTop: 4 }}>
                    <Text type="secondary" style={{ fontSize: '11px' }}>
                      {project.fileCount} files • {(project.totalSize / 1024 / 1024).toFixed(1)} MB
                    </Text>
                    <Tag>{project.storageProvider}</Tag>
                  </Space>
                </div>
              }
            />
          </List.Item>
        )}
      />
    </div>
  );

  // 渲染文件列表
  const renderFilesList = () => {
    if (!activeProject) {
      return (
        <div className="no-project-selected">
          <Text type="secondary">{t('cloudStorage.selectProjectToViewFiles')}</Text>
        </div>
      );
    }

    const columns = [
      {
        title: t('cloudStorage.fileName'),
        dataIndex: 'name',
        key: 'name',
        render: (name: string, file: CloudFile) => (
          <Space>
            <FileOutlined />
            <Text>{name}</Text>
            {file.status === FileStatus.SYNCING && (
              <Tag color="blue">Syncing</Tag>
            )}
            {file.status === FileStatus.CONFLICT && (
              <Tag color="red">Conflict</Tag>
            )}
          </Space>
        )
      },
      {
        title: t('cloudStorage.size'),
        dataIndex: 'size',
        key: 'size',
        render: (size: number) => `${(size / 1024).toFixed(1)} KB`
      },
      {
        title: t('cloudStorage.lastModified'),
        dataIndex: 'updatedAt',
        key: 'updatedAt',
        render: (timestamp: number) => new Date(timestamp).toLocaleString()
      },
      {
        title: t('cloudStorage.actions'),
        key: 'actions',
        render: (_, file: CloudFile) => (
          <Space>
            <Button
              type="text"
              size="small"
              icon={<DownloadOutlined />}
              onClick={() => handleDownloadFile(file.id)}
            />
            <Button
              type="text"
              size="small"
              icon={<ShareAltOutlined />}
            />
            <Button
              type="text"
              size="small"
              icon={<HistoryOutlined />}
            />
            <Button
              type="text"
              size="small"
              icon={<DeleteOutlined />}
              danger
              onClick={() => handleDeleteFile(file.id)}
            />
          </Space>
        )
      }
    ];

    return (
      <div className="files-list">
        <div className="files-header">
          <Space>
            <Dragger
              multiple
              showUploadList={false}
              beforeUpload={() => false}
              onChange={({ fileList }) => handleUploadFiles(fileList)}
              style={{ width: 200 }}
            >
              <Button type="primary">
                <UploadOutlined />
                {t('cloudStorage.uploadFiles')}
              </Button>
            </Dragger>
            
            <Button onClick={handleForceSync} loading={!!syncProgress}>
              <SyncOutlined />
              {t('cloudStorage.sync')}
            </Button>
          </Space>
        </div>

        {uploadProgress && (
          <Alert
            message={t('cloudStorage.uploading')}
            description={
              <div>
                <Text>{uploadProgress.fileName}</Text>
                <Progress
                  percent={uploadProgress.percentage}
                  size="small"
                  style={{ marginTop: 4 }}
                />
              </div>
            }
            type="info"
            style={{ marginBottom: 16 }}
          />
        )}

        {downloadProgress && (
          <Alert
            message={t('cloudStorage.downloading')}
            description={
              <div>
                <Text>{downloadProgress.fileName}</Text>
                <Progress
                  percent={downloadProgress.percentage}
                  size="small"
                  style={{ marginTop: 4 }}
                />
              </div>
            }
            type="info"
            style={{ marginBottom: 16 }}
          />
        )}

        {syncProgress && (
          <Alert
            message={t('cloudStorage.syncing')}
            description={
              <div>
                <Text>{syncProgress.phase}: {syncProgress.currentFile}</Text>
                <Progress
                  percent={(syncProgress.filesProcessed / syncProgress.totalFiles) * 100}
                  size="small"
                  style={{ marginTop: 4 }}
                />
              </div>
            }
            type="info"
            style={{ marginBottom: 16 }}
          />
        )}

        <Table
          dataSource={files}
          columns={columns}
          rowKey="id"
          size="small"
          pagination={{ pageSize: 10 }}
        />
      </div>
    );
  };

  // 渲染存储统计
  const renderStorageStats = () => (
    <div className="storage-stats">
      <Row gutter={[16, 16]}>
        <Col span={12}>
          <Statistic
            title={t('cloudStorage.totalProjects')}
            value={projects.length}
            prefix={<FolderOutlined />}
          />
        </Col>
        <Col span={12}>
          <Statistic
            title={t('cloudStorage.totalFiles')}
            value={storageUsage.fileCount || 0}
            prefix={<FileOutlined />}
          />
        </Col>
        <Col span={24}>
          <div className="storage-usage">
            <Text type="secondary">{t('cloudStorage.storageUsage')}</Text>
            <Progress
              percent={(storageUsage.usedSize / storageUsage.totalSize) * 100}
              strokeColor="#1890ff"
              style={{ marginTop: 8 }}
            />
            <Text type="secondary" style={{ fontSize: '12px' }}>
              {(storageUsage.usedSize / 1024 / 1024 / 1024).toFixed(2)} GB / 
              {(storageUsage.totalSize / 1024 / 1024 / 1024).toFixed(0)} GB
            </Text>
          </div>
        </Col>
      </Row>

      <Divider />

      <div className="sync-status">
        <Space>
          {isOnline ? (
            <Badge status="success" text={t('cloudStorage.online')} />
          ) : (
            <Badge status="error" text={t('cloudStorage.offline')} />
          )}
          
          {syncStatus.isActive && (
            <Badge status="processing" text={t('cloudStorage.syncing')} />
          )}
          
          {syncStatus.queueSize > 0 && (
            <Tag color="blue">{syncStatus.queueSize} pending</Tag>
          )}
        </Space>
        
        {syncStatus.lastSync && (
          <Text type="secondary" style={{ fontSize: '12px', display: 'block', marginTop: 8 }}>
            Last sync: {new Date(syncStatus.lastSync).toLocaleString()}
          </Text>
        )}
      </div>
    </div>
  );

  return (
    <Modal
      title={
        <Space>
          <CloudOutlined />
          {t('cloudStorage.cloudStorage')}
          {!isOnline && (
            <Badge status="error" text={t('cloudStorage.offline')} />
          )}
        </Space>
      }
      open={visible}
      onCancel={onClose}
      width={1200}
      footer={[
        <Button key="import" icon={<ImportOutlined />}>
          {t('cloudStorage.import')}
        </Button>,
        <Button key="cleanup" onClick={() => cloudStorageService.cleanupTempFiles()}>
          <DeleteOutlined />
          {t('cloudStorage.cleanup')}
        </Button>,
        <Button key="close" onClick={onClose}>
          {t('common.close')}
        </Button>
      ]}
      className="cloud-storage-panel"
    >
      <Row gutter={[16, 0]} style={{ height: '70vh' }}>
        <Col span={8}>
          {renderProjectsList()}
        </Col>
        <Col span={12}>
          {renderFilesList()}
        </Col>
        <Col span={4}>
          {renderStorageStats()}
        </Col>
      </Row>

      {/* 新建项目对话框 */}
      <Modal
        title={t('cloudStorage.createNewProject')}
        open={newProjectModalVisible}
        onCancel={() => setNewProjectModalVisible(false)}
        onOk={() => form.submit()}
      >
        <Form form={form} layout="vertical" onFinish={handleCreateProject}>
          <Form.Item
            name="name"
            label={t('cloudStorage.projectName')}
            rules={[{ required: true }]}
          >
            <Input />
          </Form.Item>
          <Form.Item name="description" label={t('cloudStorage.description')}>
            <Input.TextArea rows={3} />
          </Form.Item>
          <Form.Item name="provider" label={t('cloudStorage.storageProvider')}>
            <Select defaultValue={StorageProvider.CUSTOM}>
              {Object.values(StorageProvider).map(provider => (
                <Option key={provider} value={provider}>{provider.toUpperCase()}</Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item name="compression" label={t('cloudStorage.compression')}>
            <Select defaultValue={CompressionAlgorithm.GZIP}>
              {Object.values(CompressionAlgorithm).map(algo => (
                <Option key={algo} value={algo}>{algo.toUpperCase()}</Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item name="encryption" valuePropName="checked" label={t('cloudStorage.enableEncryption')}>
            <Switch />
          </Form.Item>
        </Form>
      </Modal>

      {/* 设置对话框 */}
      <Modal
        title={t('cloudStorage.storageSettings')}
        open={settingsModalVisible}
        onCancel={() => setSettingsModalVisible(false)}
        footer={null}
      >
        <Form layout="vertical">
          <Form.Item label={t('cloudStorage.syncStrategy')}>
            <Select defaultValue={SyncStrategy.AUTO}>
              {Object.values(SyncStrategy).map(strategy => (
                <Option key={strategy} value={strategy}>{strategy.toUpperCase()}</Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item label={t('cloudStorage.autoSyncInterval')}>
            <Slider min={1} max={60} defaultValue={5} />
          </Form.Item>
          <Form.Item label={t('cloudStorage.maxFileSize')}>
            <Slider min={1} max={1000} defaultValue={100} />
          </Form.Item>
        </Form>
      </Modal>
    </Modal>
  );
};

export default CloudStoragePanel;
