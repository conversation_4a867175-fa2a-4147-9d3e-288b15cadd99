/**
 * 移动端区块链面板组件
 */

import React, { useState } from 'react';
import {
  Drawer,
  Tabs,
  Button,
  Space,
  Badge,
  FloatButton,
  Affix,
  Card,
  Typography,
  Grid,
  message,
} from 'antd';
import {
  WalletOutlined,
  ShoppingOutlined,
  HistoryOutlined,
  SettingOutlined,
  QrcodeOutlined,
  MobileOutlined,
  CloseOutlined,
} from '@ant-design/icons';

import { useBlockchain } from '../../../hooks/useBlockchain';
import { useNFT } from '../../../hooks/useNFT';
import { useI18n } from '../../../hooks/useI18n';
// 注意：以下组件暂时未实现，使用占位符组件
// import { MobileWalletConnection } from './MobileWalletConnection';
// import { MobileNFTGrid } from './MobileNFTGrid';
// import { MobileTransactionHistory } from './MobileTransactionHistory';
// import { MobileMarketplace } from './MobileMarketplace';
// import { MobileSettings } from './MobileSettings';

import './MobileBlockchainPanel.css';

const { Text } = Typography;
const { useBreakpoint } = Grid;

// 占位符组件
const MobileWalletConnection: React.FC = () => (
  <div style={{ padding: '16px', textAlign: 'center' }}>
    <Text type="secondary">钱包连接组件开发中...</Text>
  </div>
);

const MobileNFTGrid: React.FC = () => (
  <div style={{ padding: '16px', textAlign: 'center' }}>
    <Text type="secondary">NFT 网格组件开发中...</Text>
  </div>
);

const MobileTransactionHistory: React.FC = () => (
  <div style={{ padding: '16px', textAlign: 'center' }}>
    <Text type="secondary">交易历史组件开发中...</Text>
  </div>
);

const MobileMarketplace: React.FC = () => (
  <div style={{ padding: '16px', textAlign: 'center' }}>
    <Text type="secondary">市场组件开发中...</Text>
  </div>
);

const MobileSettings: React.FC = () => (
  <div style={{ padding: '16px', textAlign: 'center' }}>
    <Text type="secondary">设置组件开发中...</Text>
  </div>
);

export interface MobileBlockchainPanelProps {
  visible?: boolean;
  onClose?: () => void;
  defaultActiveTab?: string;
}

export const MobileBlockchainPanel: React.FC<MobileBlockchainPanelProps> = ({
  visible = false,
  onClose,
  defaultActiveTab = 'wallet',
}) => {
  const { t } = useI18n();
  const screens = useBreakpoint();
  const { isConnected, balance } = useBlockchain();
  const { nfts } = useNFT();

  const [activeTab, setActiveTab] = useState(defaultActiveTab);
  const [isFullScreen, setIsFullScreen] = useState(false);

  // 检测是否为移动设备
  const isMobile = !screens.md;

  // 标签页配置
  const tabItems = [
    {
      key: 'wallet',
      label: (
        <Space>
          <WalletOutlined />
          <span className="mobile-tab-label">{t('blockchain.wallet.title')}</span>
        </Space>
      ),
      children: <MobileWalletConnection />,
    },
    {
      key: 'nfts',
      label: (
        <Space>
          <Badge count={nfts.length} size="small" offset={[10, 0]}>
            <ShoppingOutlined />
          </Badge>
          <span className="mobile-tab-label">{t('blockchain.nft.title')}</span>
        </Space>
      ),
      children: <MobileNFTGrid />,
    },
    {
      key: 'marketplace',
      label: (
        <Space>
          <ShoppingOutlined />
          <span className="mobile-tab-label">{t('blockchain.marketplace.title')}</span>
        </Space>
      ),
      children: <MobileMarketplace />,
    },
    {
      key: 'transactions',
      label: (
        <Space>
          <HistoryOutlined />
          <span className="mobile-tab-label">{t('blockchain.transactions.title')}</span>
        </Space>
      ),
      children: <MobileTransactionHistory />,
    },
    {
      key: 'settings',
      label: (
        <Space>
          <SettingOutlined />
          <span className="mobile-tab-label">{t('common.settings')}</span>
        </Space>
      ),
      children: <MobileSettings />,
    },
  ];

  // 处理标签页切换
  const handleTabChange = (key: string) => {
    setActiveTab(key);
  };

  // 切换全屏模式
  const toggleFullScreen = () => {
    setIsFullScreen(!isFullScreen);
  };

  // 渲染头部
  const renderHeader = () => (
    <div className="mobile-blockchain-header">
      <div className="mobile-blockchain-title">
        <Space>
          <WalletOutlined />
          <Text strong>{t('blockchain.title')}</Text>
        </Space>
      </div>
      
      <div className="mobile-blockchain-status">
        {isConnected ? (
          <Space direction="vertical" size={0}>
            <Text type="success" className="mobile-status-text">
              {t('blockchain.wallet.connected')}
            </Text>
            <Text type="secondary" className="mobile-balance-text">
              {balance} ETH
            </Text>
          </Space>
        ) : (
          <Text type="warning" className="mobile-status-text">
            {t('blockchain.wallet.disconnected')}
          </Text>
        )}
      </div>

      <div className="mobile-blockchain-actions">
        <Space>
          <Button
            type="text"
            icon={isFullScreen ? <CloseOutlined /> : <MobileOutlined />}
            onClick={toggleFullScreen}
            className="mobile-action-button"
          />
          {onClose && (
            <Button
              type="text"
              icon={<CloseOutlined />}
              onClick={onClose}
              className="mobile-action-button"
            />
          )}
        </Space>
      </div>
    </div>
  );

  // 渲染快速操作按钮
  const renderQuickActions = () => (
    <div className="mobile-quick-actions">
      <Space wrap>
        <Button
          type="primary"
          icon={<QrcodeOutlined />}
          size="small"
          onClick={() => setActiveTab('wallet')}
        >
          {t('blockchain.wallet.connect')}
        </Button>
        <Button
          icon={<ShoppingOutlined />}
          size="small"
          onClick={() => setActiveTab('nfts')}
        >
          {t('blockchain.nft.mint')}
        </Button>
        <Button
          icon={<ShoppingOutlined />}
          size="small"
          onClick={() => setActiveTab('marketplace')}
        >
          {t('blockchain.marketplace.browse')}
        </Button>
      </Space>
    </div>
  );

  // 移动端抽屉模式
  if (isMobile) {
    return (
      <Drawer
        title={renderHeader()}
        placement="bottom"
        height={isFullScreen ? '100vh' : '80vh'}
        open={visible}
        onClose={onClose}
        className="mobile-blockchain-drawer"
        headerStyle={{ padding: '12px 16px' }}
        bodyStyle={{ padding: 0 }}
        closable={false}
      >
        <div className="mobile-blockchain-content">
          {!isConnected && renderQuickActions()}
          
          <Tabs
            activeKey={activeTab}
            onChange={handleTabChange}
            items={tabItems}
            className="mobile-blockchain-tabs"
            tabPosition="bottom"
            size="small"
          />
        </div>
      </Drawer>
    );
  }

  // 平板端卡片模式
  return (
    <Card
      className="mobile-blockchain-card"
      title={renderHeader()}
      extra={
        onClose && (
          <Button
            type="text"
            icon={<CloseOutlined />}
            onClick={onClose}
          />
        )
      }
      bodyStyle={{ padding: 0 }}
    >
      <div className="mobile-blockchain-content">
        {!isConnected && renderQuickActions()}
        
        <Tabs
          activeKey={activeTab}
          onChange={handleTabChange}
          items={tabItems}
          className="mobile-blockchain-tabs"
          size="middle"
        />
      </div>
    </Card>
  );
};

// 浮动按钮组件
export const MobileBlockchainFloatButton: React.FC<{
  onOpen?: () => void;
}> = ({ onOpen }) => {
  const { t } = useI18n();
  const { isConnected } = useBlockchain();
  const { nfts } = useNFT();

  return (
    <FloatButton.Group
      trigger="click"
      type="primary"
      icon={<WalletOutlined />}
      tooltip={t('blockchain.title')}
      className="mobile-blockchain-float-button"
    >
      <FloatButton
        icon={<WalletOutlined />}
        tooltip={t('blockchain.wallet.title')}
        onClick={() => onOpen?.()}
        badge={{ count: isConnected ? 1 : 0, dot: true }}
      />
      <FloatButton
        icon={<ShoppingOutlined />}
        tooltip={t('blockchain.nft.title')}
        onClick={() => onOpen?.()}
        badge={{ count: nfts.length }}
      />
      <FloatButton
        icon={<HistoryOutlined />}
        tooltip={t('blockchain.transactions.title')}
        onClick={() => onOpen?.()}
      />
    </FloatButton.Group>
  );
};

// 移动端区块链状态栏
export const MobileBlockchainStatusBar: React.FC = () => {
  const { isConnected, currentAccount, balance, currentNetwork } = useBlockchain();

  if (!isConnected) {
    return null;
  }

  return (
    <Affix offsetTop={0}>
      <div className="mobile-blockchain-status-bar">
        <Space size="small">
          <Badge status="success" />
          <Text className="mobile-status-account">
            {currentAccount?.slice(0, 6)}...{currentAccount?.slice(-4)}
          </Text>
          <Text className="mobile-status-balance">
            {parseFloat(balance).toFixed(4)} {currentNetwork?.nativeCurrency?.symbol}
          </Text>
        </Space>
      </div>
    </Affix>
  );
};

// 移动端区块链快捷操作
export const MobileBlockchainShortcuts: React.FC<{
  onActionClick?: (action: string) => void;
}> = ({ onActionClick }) => {
  const { t } = useI18n();
  const { isConnected } = useBlockchain();

  const shortcuts = [
    {
      key: 'connect',
      icon: <WalletOutlined />,
      label: t('blockchain.wallet.connect'),
      disabled: isConnected,
    },
    {
      key: 'mint',
      icon: <ShoppingOutlined />,
      label: t('blockchain.nft.mint'),
      disabled: !isConnected,
    },
    {
      key: 'marketplace',
      icon: <ShoppingOutlined />,
      label: t('blockchain.marketplace.browse'),
      disabled: false,
    },
    {
      key: 'history',
      icon: <HistoryOutlined />,
      label: t('blockchain.transactions.title'),
      disabled: !isConnected,
    },
  ];

  return (
    <div className="mobile-blockchain-shortcuts">
      <Space wrap>
        {shortcuts.map((shortcut) => (
          <Button
            key={shortcut.key}
            type="default"
            icon={shortcut.icon}
            size="small"
            disabled={shortcut.disabled}
            onClick={() => onActionClick?.(shortcut.key)}
            className="mobile-shortcut-button"
          >
            {shortcut.label}
          </Button>
        ))}
      </Space>
    </div>
  );
};
